import { Injectable } from '@nestjs/common';
import * as sgMail from '@sendgrid/mail';
import { HttpException, HttpStatus } from '@nestjs/common';

@Injectable()
export class MailService {
  constructor() {
    sgMail.setApiKey(process.env.SENDGRID_API_KEY!);
    console.log(process.env.SENDGRID_API_KEY);
  }

  async sendVerificationCodeEmail(
    email: string,
    verificationCode: string,
    referralCode: string,
  ): Promise<void> {
    const verificationLink = `http://34.68.40.124:3000/api/auth/verify-email?code=${verificationCode}&email=${encodeURIComponent(email)}&referralCode=${encodeURIComponent(referralCode)}`;

    const msg = {
      to: email,
      from: `TRADE REWARDS <${process.env.EMAIL}>`,
      subject: 'Your TRADE REWARDS Verification Link',
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #F4F4F4; padding: 30px;">
        <div style="max-width: 600px; margin: auto; background-color: #FFFFFF; border-radius: 8px; padding: 30px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">
          <h2 style="text-align: center; color: #2C3E50;">Email Verification</h2>
          <p style="font-size: 16px; color: #333;">Hi there,</p>
          <p style="font-size: 16px; color: #333;">Thank you for registering with <strong>TRADE REWARDS</strong>.</p>
          <p style="font-size: 16px; color: #333;">Please click the button below to verify your email:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationLink}" style="display: inline-block; background-color: #2C3E50; color: #FFFFFF; padding: 12px 24px; border-radius: 6px; text-decoration: none; font-size: 16px;">
              Verify Email
            </a>
          </div>
          <p style="font-size: 16px; color: #333;">If you did not create an account, no further action is required.</p>
          <br>
          <p style="font-size: 16px; color: #333;">Best regards,</p>
          <p style="font-size: 16px; color: #333;"><strong>The TRADE REWARDS Team</strong></p>
          <hr style="margin-top: 30px; border: none; border-top: 1px solid #eee;" />
          <p style="font-size: 12px; color: #999; text-align: center;">&copy; ${new Date().getFullYear()} TRADE REWARDS. All rights reserved.</p>
        </div>
      </div>
    `,
    };

    try {
      await sgMail.send(msg);
      console.log('Verification email sent successfully to:', email);
    } catch (error) {
      console.error('SendGrid Error:', error.response?.body || error.message);
      throw new HttpException(
        'Failed to send verification email',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async sendResetPasswordEmail(
    email: string,
    resetCode: string,
  ): Promise<void> {
    const verificationLink = `http://34.68.40.124:3000/api/auth/verify-reset-password?code=${resetCode}&email=${encodeURIComponent(email)}`;
    const msg = {
      to: email,
      from: `TRADE REWARDS <${process.env.EMAIL}>`,
      subject: 'Reset Your TRADE REWARDS Password',
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #F4F4F4; padding: 30px;">
        <div style="max-width: 600px; margin: auto; background-color: #FFFFFF; border-radius: 8px; padding: 30px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">
          <h2 style="text-align: center; color: #2C3E50;">Reset Your Password</h2>
          <p style="font-size: 16px; color: #333;">Hi there,</p>
          <p style="font-size: 16px; color: #333;">You have requested to reset your password for your <strong>TRADE REWARDS</strong> account.</p>
          <p style="font-size: 16px; color: #333;">Please click the button below to reset your password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationLink}" style="display: inline-block; background-color: #2C3E50; color: #FFFFFF; padding: 12px 24px; border-radius: 6px; text-decoration: none; font-size: 16px;">
              Reset Password
            </a>
          </div>
          <p style="font-size: 16px; color: #333;">If you did not request a password reset, please ignore this email.</p>
          <br>
          <p style="font-size: 16px; color: #333;">Best regards,</p>
          <p style="font-size: 16px; color: #333;"><strong>The TRADE REWARDS Team</strong></p>
          <hr style="margin-top: 30px; border: none; border-top: 1px solid #eee;" />
          <p style="font-size: 12px; color: #999; text-align: center;">&copy; ${new Date().getFullYear()} TRADE REWARDS. All rights reserved.</p>
        </div>
      </div>
    `,
    };

    try {
      await sgMail.send(msg);
      console.log('Reset password email sent to:', email);
    } catch (error) {
      console.error('SendGrid Error:', error.response?.body || error.message);
      throw error;
    }
  }

  async sendSupportEmail(
    email: string,
    name: string,
    message: string,
  ): Promise<void> {
    const msg = {
      to: email,
      from: `TRADE REWARDS <${process.env.EMAIL}>`,
      subject: 'Support Request Received - TRADE REWARDS',
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #F4F4F4; padding: 30px;">
        <div style="max-width: 600px; margin: auto; background-color: #FFFFFF; border-radius: 8px; padding: 30px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">
          <h2 style="text-align: center; color: #2C3E50;">Support Request Received</h2>
          <p style="font-size: 16px; color: #333;">Hi ${name},</p>
          <p style="font-size: 16px; color: #333;">Thank you for contacting <strong>TRADE REWARDS</strong> support.</p>
          <p style="font-size: 16px; color: #333;">We have received your message and our team will get back to you shortly.</p>
          <div style="background-color: #F9F9F9; padding: 15px; margin: 20px 0; border-left: 4px solid #2C3E50;">
            <p style="font-size: 16px; color: #333; margin: 0;"><strong>Your Message:</strong></p>
            <p style="font-size: 16px; color: #333; margin: 0;">${message}</p>
          </div>
          <p style="font-size: 16px; color: #333;">We appreciate your patience.</p>
          <br>
          <p style="font-size: 16px; color: #333;">Best regards,</p>
          <p style="font-size: 16px; color: #333;"><strong>The TRADE REWARDS Support Team</strong></p>
          <hr style="margin-top: 30px; border: none; border-top: 1px solid #eee;" />
          <p style="font-size: 12px; color: #999; text-align: center;">&copy; ${new Date().getFullYear()} TRADE REWARDS. All rights reserved.</p>
        </div>
      </div>
    `,
    };

    try {
      await sgMail.send(msg);
      console.log('Support confirmation email sent to:', email);
    } catch (error) {
      console.error('SendGrid Error:', error.response?.body || error.message);
      throw new HttpException(
        'Failed to send support confirmation email',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async sendCustomEmail(
    toEmail: string,
    fromEmail: string,
    subject: string,
    body: string,
    ccEmails?: string,
    brokerDefaultCC?: string,
  ): Promise<void> {
    const msg: any = {
      to: toEmail,
      from: `TRADE REWARDS BROKER MAIL<${process.env.EMAIL}>`,
      subject: subject,
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #F4F4F4; padding: 30px;">
        <div style="max-width: 600px; margin: auto; background-color: #FFFFFF; border-radius: 8px; padding: 30px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">
          <div style="margin-bottom: 20px;">
            <p style="font-size: 14px; color: #666; margin: 0;"><strong>From:</strong> ${fromEmail}</p>
          </div>
          <div style="font-size: 16px; color: #333; line-height: 1.6;">
            ${body.replace(/\n/g, '<br>')}
          </div>
          <hr style="margin-top: 30px; border: none; border-top: 1px solid #eee;" />
          <p style="font-size: 12px; color: #999; text-align: center;">&copy; ${new Date().getFullYear()} TRADE REWARDS. All rights reserved.</p>
        </div>
      </div>
    `,
    };

    // Use broker's defaultCC or <NAME_EMAIL>
    const ccArray = [brokerDefaultCC || '<EMAIL>'];
    
    if (ccEmails && ccEmails.trim()) {
      const additionalCCs = ccEmails
        .split(',')
        .map((email) => email.trim())
        .filter((email) => email);
      ccArray.push(...additionalCCs);
    }

    if (ccArray.length > 0) {
      msg.cc = ccArray;
    }

    try {
      await sgMail.send(msg);
      console.log('CC sent to:', ccArray.join(', '));
    } catch (error) {
      throw new HttpException(
        'Failed to send custom email',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async sendManagerCredentialsEmail(
    email: string,
    firstName: string,
    lastName: string,
    password: string,
    roles: string[],
  ): Promise<void> {
    const msg = {
      to: email,
      from: `TRADE REWARDS <${process.env.EMAIL}>`,
      subject: 'Your Manager Account Credentials - TRADE REWARDS',
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #F4F4F4; padding: 30px;">
        <div style="max-width: 600px; margin: auto; background-color: #FFFFFF; border-radius: 8px; padding: 30px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">
          <h2 style="text-align: center; color: #2C3E50;">Manager Account Created</h2>
          <p style="font-size: 16px; color: #333;">Hi ${firstName} ${lastName},</p>
          <p style="font-size: 16px; color: #333;">Welcome to <strong>TRADE REWARDS</strong>! Your manager account has been successfully created.</p>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #2C3E50; margin-top: 0;">Your Login Credentials:</h3>
            <p style="margin: 10px 0;"><strong>Email:</strong> ${email}</p>
            <p style="margin: 10px 0;"><strong>Password:</strong> ${password}</p>
            <p style="margin: 10px 0;"><strong>Assigned Roles:</strong> ${roles.join(', ')}</p>
          </div>

          <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <p style="margin: 0; color: #856404;"><strong>Important:</strong> Please change your password after your first login for security purposes.</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="http://34.68.40.124:3000" style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">Login to Dashboard</a>
          </div>

          <p style="font-size: 14px; color: #666;">If you have any questions or need assistance, please contact our support team.</p>

          <hr style="margin-top: 30px; border: none; border-top: 1px solid #eee;" />
          <p style="font-size: 12px; color: #999; text-align: center;">&copy; ${new Date().getFullYear()} TRADE REWARDS. All rights reserved.</p>
        </div>
      </div>
    `,
    };

    try {
      await sgMail.send(msg);
      console.log('Manager credentials email sent to:', email);
    } catch (error) {
      console.error('SendGrid Error:', error.response?.body || error.message);
      throw new HttpException(
        'Failed to send manager credentials email',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
